"""
规则查询服务 - 重构版本
基于新的三表结构（RuleTemplate、RuleDetail、RuleFieldMetadata）
提供规则查询相关的业务逻辑，包括关键词匹配和明细规则ID获取
"""

from typing import Any

from sqlalchemy import and_, func, or_
from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from models.database import (
    RuleDetail,
    RuleDetailStatusEnum,
    RuleTemplate,
    RuleTemplateStatusEnum,
)


class RuleQueryService:
    """规则查询服务类 - 重构版本"""

    @staticmethod
    def get_rule_detail_ids_by_rule_key(session: Session, rule_key: str) -> list[str]:
        """
        根据规则键获取所有活跃的规则明细ID

        Args:
            session: 数据库会话
            rule_key: 规则模板键

        Returns:
            List[str]: 规则明细ID列表
        """
        try:
            # 查询活跃的规则明细
            rule_details = (
                session.query(RuleDetail.rule_id)
                .filter(and_(RuleDetail.rule_key == rule_key, RuleDetail.status == RuleDetailStatusEnum.ACTIVE))
                .all()
            )

            rule_ids = [detail.rule_id for detail in rule_details]
            logger.debug(f"获取规则明细ID: rule_key={rule_key}, count={len(rule_ids)}")
            return rule_ids

        except Exception as e:
            logger.error(f"获取规则明细ID失败: rule_key={rule_key}, error={e}")
            return []

    @staticmethod
    def count_rule_details_by_rule_key(session: Session, rule_key: str) -> int:
        """
        计算指定规则的明细数量

        Args:
            session: 数据库会话
            rule_key: 规则模板键

        Returns:
            int: 规则明细数量
        """
        try:
            count = (
                session.query(RuleDetail)
                .filter(and_(RuleDetail.rule_key == rule_key, RuleDetail.status == RuleDetailStatusEnum.ACTIVE))
                .count()
            )

            logger.debug(f"统计规则明细数量: rule_key={rule_key}, count={count}")
            return count

        except Exception as e:
            logger.error(f"统计规则明细数量失败: rule_key={rule_key}, error={e}")
            return 0

    @staticmethod
    def search_rules_by_keyword(session: Session, keyword: str | None = None) -> dict[str, Any]:
        """
        根据关键词搜索规则

        Args:
            session: 数据库会话
            keyword: 搜索关键词

        Returns:
            Dict: 搜索结果
        """
        try:
            # 构建基础查询
            query = session.query(RuleTemplate).filter(RuleTemplate.status == RuleTemplateStatusEnum.READY)

            # 添加关键词过滤
            if keyword:
                keyword_pattern = f"%{keyword}%"
                query = query.filter(
                    or_(
                        func.lower(RuleTemplate.rule_key).like(func.lower(keyword_pattern)),
                        func.lower(RuleTemplate.name).like(func.lower(keyword_pattern)),
                    )
                )

            # 按rule_key排序
            templates = query.order_by(RuleTemplate.rule_key).all()

            if not templates:
                return {"success": False, "message": "未找到匹配的规则", "data": []}

            # 构建结果
            results = []
            for template in templates:
                # 获取该规则的明细数量
                detail_count = RuleQueryService.count_rule_details_by_rule_key(session, template.rule_key)

                results.append(
                    {
                        "rule_key": template.rule_key,
                        "rule_name": template.name,
                        "description": template.description,
                        "status": template.status.value,
                        "detail_count": detail_count,
                        "created_at": template.created_at.isoformat() if template.created_at else None,
                        "updated_at": template.updated_at.isoformat() if template.updated_at else None,
                    }
                )

            return {"success": True, "message": f"找到 {len(results)} 个匹配的规则", "data": results}

        except Exception as e:
            logger.error(f"搜索规则失败: keyword={keyword}, error={e}")
            return {"success": False, "message": f"搜索失败: {str(e)}", "data": []}

    @staticmethod
    def get_active_rules_count(session: Session) -> int:
        """
        获取生效规则数量

        Args:
            session: 数据库会话

        Returns:
            int: 生效规则数量
        """
        try:
            count = session.query(RuleTemplate).filter(RuleTemplate.status == RuleTemplateStatusEnum.READY).count()
            return count
        except Exception as e:
            logger.error(f"获取生效规则数量失败: {str(e)}")
            return 0

    @staticmethod
    def get_rule_info_by_key(session: Session, rule_key: str) -> dict[str, Any] | None:
        """
        根据规则键获取规则详细信息

        Args:
            session: 数据库会话
            rule_key: 规则模板键

        Returns:
            Optional[Dict]: 规则详细信息，如果未找到返回None
        """
        try:
            template = (
                session.query(RuleTemplate)
                .filter(and_(RuleTemplate.rule_key == rule_key, RuleTemplate.status == RuleTemplateStatusEnum.READY))
                .first()
            )

            if not template:
                return None

            # 获取规则明细数量
            detail_count = RuleQueryService.count_rule_details_by_rule_key(session, rule_key)

            return {
                "rule_key": template.rule_key,
                "rule_name": template.name,
                "description": template.description,
                "status": template.status.value,
                "detail_count": detail_count,
                "created_at": template.created_at.isoformat() if template.created_at else None,
                "updated_at": template.updated_at.isoformat() if template.updated_at else None,
            }

        except Exception as e:
            logger.error(f"获取规则信息失败: rule_key={rule_key}, error={e}")
            return None

    @staticmethod
    def get_rule_details_with_pagination(
        session: Session, rule_key: str, page: int = 1, page_size: int = 20, filters: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """
        分页获取规则明细数据

        Args:
            session: 数据库会话
            rule_key: 规则模板键
            page: 页码（从1开始）
            page_size: 每页大小
            filters: 过滤条件

        Returns:
            dict: 分页结果
        """
        try:
            # 验证规则是否存在
            template = (
                session.query(RuleTemplate)
                .filter(and_(RuleTemplate.rule_key == rule_key, RuleTemplate.status == RuleTemplateStatusEnum.READY))
                .first()
            )

            if not template:
                return {"success": False, "message": f"规则 '{rule_key}' 不存在", "data": None}

            # 构建查询
            query = session.query(RuleDetail).filter(
                and_(RuleDetail.rule_key == rule_key, RuleDetail.status == RuleDetailStatusEnum.ACTIVE)
            )

            # 应用过滤条件
            if filters:
                for field, value in filters.items():
                    if hasattr(RuleDetail, field) and value is not None:
                        query = query.filter(getattr(RuleDetail, field) == value)

            # 计算总数
            total_count = query.count()

            # 分页
            offset = (page - 1) * page_size
            details = query.offset(offset).limit(page_size).all()

            # 转换为字典
            detail_list = [detail.to_dict() for detail in details]

            return {
                "success": True,
                "message": "查询成功",
                "data": {
                    "rule_key": rule_key,
                    "rule_name": template.name,
                    "total_count": total_count,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total_count + page_size - 1) // page_size,
                    "details": detail_list,
                },
            }

        except Exception as e:
            logger.error(f"分页查询规则明细失败: rule_key={rule_key}, error={e}")
            return {"success": False, "message": f"查询失败: {str(e)}", "data": None}
