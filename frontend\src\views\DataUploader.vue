<template>
  <div class="data-uploader">
    <!-- 页面头部 -->
    <el-card class="header-card" shadow="never">
      <div class="page-header">
        <div class="header-info">
          <h2 class="page-title" data-testid="page-title">数据上传与校验</h2>
          <p class="page-subtitle" data-testid="rule-selector">规则: <strong>{{ ruleName || ruleKey }}</strong></p>
        </div>
        <el-button @click="goBack" :icon="ArrowLeft">返回仪表盘</el-button>
      </div>
    </el-card>

    <!-- 步骤指示器 -->
    <RegistrationSteps
      :current-step="currentStep"
      :step-status="stepStatus"
      :has-file="!!selectedFile"
      :file-name="selectedFile?.name"
      :valid-count="allValidRows.length"
      :invalid-count="allInvalidRows.length"
      :total-count="allValidRows.length + allInvalidRows.length"
      :progress="taskProgress"
      :progress-status="taskProgressStatus"
      :progress-message="taskProgressMessage"
      :progress-detail="taskProgressDetail"
      :is-success="isTaskSuccess"
      :result-message="taskResultMessage"
      @restart="handleRestart"
      @back-to-dashboard="goBack"
    />

    <!-- 主要内容区域 -->
    <el-card class="content-card" shadow="hover">
      <!-- 文件上传步骤 -->
      <div v-if="currentStep === 0" class="upload-section" data-testid="upload-area">
        <!-- 上传模式选择组件 -->
        <UploadModeSelector
          v-model="uploadMode"
          @change="handleModeChange"
          data-testid="upload-mode-selector"
        />

        <!-- 文件上传区域组件 -->
        <FileUploadArea
          :selected-file="selectedFile"
          :processing="processing"
          :file-status="fileStatus"
          @file-change="handleFileChange"
          @file-clear="clearFile"
          @process-file="processFile"
          data-testid="file-input"
        />
      </div>

      <!-- 数据预览步骤 -->
      <div v-else-if="currentStep === 1" class="preview-section">
        <!-- 全量上传预览 -->
        <div v-if="uploadMode === 'full'" class="full-upload-preview">
          <!-- 数据预览 -->
          <el-card class="data-preview-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span>数据预览</span>
                <div class="data-stats">
                  <el-tag type="success" size="small">有效数据: {{ allValidRows.length }}</el-tag>
                  <el-tag type="danger" size="small" v-if="allInvalidRows.length > 0">
                    无效数据: {{ allInvalidRows.length }}
                  </el-tag>
                  <el-tag type="info" size="small">
                    总计: {{ allValidRows.length + allInvalidRows.length }}
                  </el-tag>
                </div>
              </div>
            </template>

            <el-tabs v-model="activeTab" type="border-card">
              <!-- 有效数据标签页 -->
              <el-tab-pane :label="`有效数据 (${allValidRows.length})`" name="valid">
                <div v-if="paginatedValidRows.length > 0">
                  <div class="data-grid">
                    <div
                      v-for="(item, index) in paginatedValidRows"
                      :key="index"
                      class="data-item-card"
                    >
                      <el-descriptions :column="3" size="small" border>
                        <el-descriptions-item
                          v-for="col in tableColumns"
                          :key="col.prop"
                          :label="col.label"
                        >
                          <!-- 字段值截断控制：当字符数超过100时显示tooltip，可在此处修改数字调整截断阈值 -->
                          <el-tooltip
                            v-if="String(item[col.prop] || '').length > 100"
                            :content="String(item[col.prop] || '')"
                            placement="top"
                            popper-class="multiline-tooltip"
                            effect="dark"
                          >
                            <span class="field-value truncated">{{ truncate(item[col.prop] || '', 100) }}</span>
                          </el-tooltip>
                          <span v-else class="field-value">{{ item[col.prop] || '-' }}</span>
                        </el-descriptions-item>
                      </el-descriptions>
                    </div>
                  </div>

                  <!-- 分页 -->
                  <el-pagination
                    v-if="allValidRows.length > pageSize"
                    layout="prev, pager, next, total"
                    :total="allValidRows.length"
                    :page-size="pageSize"
                    v-model:current-page="validCurrentPage"
                    class="pagination"
                    background
                  />
                </div>
                <el-empty v-else description="暂无有效数据" />
              </el-tab-pane>

              <!-- 无效数据标签页 -->
              <el-tab-pane :label="`无效数据 (${allInvalidRows.length})`" name="invalid">
                <div v-if="paginatedInvalidRows.length > 0">
                  <div class="data-grid">
                    <div
                      v-for="item in paginatedInvalidRows"
                      :key="item.id"
                      class="data-item-card invalid"
                    >
                      <el-alert
                        :title="`行号: ${item.rowNumber} | 错误: ${item.error}`"
                        type="error"
                        :closable="false"
                        show-icon
                        class="error-alert"
                      />
                      <el-descriptions :column="3" size="small" border>
                        <el-descriptions-item
                          v-for="col in tableColumns"
                          :key="col.prop"
                          :label="col.label"
                        >
                          <div
                            class="editable-cell"
                            @click="openEditDialog(item, col.prop, col.label)"
                          >
                            <!-- 无效数据字段值截断控制：当字符数超过100时显示tooltip，可在此处修改数字调整截断阈值 -->
                            <el-tooltip
                              v-if="String(item.data[col.prop] || '').length > 100"
                              :content="String(item.data[col.prop] || '')"
                              placement="top"
                              popper-class="multiline-tooltip"
                              effect="dark"
                            >
                              <span class="field-value truncated">{{ truncate(item.data[col.prop] || '', 100) }}</span>
                            </el-tooltip>
                            <span v-else class="field-value">{{ item.data[col.prop] || '-' }}</span>
                            <el-icon class="edit-icon"><Edit /></el-icon>
                          </div>
                        </el-descriptions-item>
                      </el-descriptions>

                      <div class="item-actions">
                        <el-button
                          type="primary"
                          size="small"
                          @click="revalidateRow(item)"
                        >
                          重新校验
                        </el-button>
                      </div>
                    </div>
                  </div>

                  <!-- 分页 -->
                  <el-pagination
                    v-if="allInvalidRows.length > pageSize"
                    layout="prev, pager, next, total"
                    :total="allInvalidRows.length"
                    :page-size="pageSize"
                    v-model:current-page="invalidCurrentPage"
                    class="pagination"
                    background
                  />
                </div>
                <el-empty v-else description="暂无无效数据" />
              </el-tab-pane>
            </el-tabs>
          </el-card>

          <div class="confirmation-section">
            <el-button
              type="primary"
              @click="nextStep"
              :disabled="allInvalidRows.length > 0"
              size="large"
            >
              下一步：确认提交 ({{ allValidRows.length }} 条)
            </el-button>
            <el-alert
              v-if="allInvalidRows.length > 0"
              title="请修复所有无效数据。所有数据行都通过校验后方可提交。"
              type="error"
              show-icon
              :closable="false"
              class="mt-3"
            />
          </div>
        </div>

        <!-- 增量上传预览 -->
        <div v-else-if="uploadMode === 'incremental'" class="incremental-upload-preview">
          <!-- 数据对比中的加载状态 -->
          <div v-if="isComparing" class="comparing-status">
            <el-card class="loading-card">
              <div class="loading-content">
                <el-icon class="loading-icon"><Loading /></el-icon>
                <div class="loading-text">
                  <h3>正在对比数据...</h3>
                  <p>系统正在将Excel文件与现有数据进行对比，请稍候</p>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 数据对比结果 -->
          <IncrementalDataComparison
            v-else-if="comparisonResult && totalOperations > 0"
            :comparison-result="comparisonResult"
            :operation-summary="operationSummary"
            :is-uploading="isIncrementalUploading"
            @confirm="handleIncrementalConfirm"
            @cancel="handleIncrementalCancel"
          />

          <!-- 无变更数据 -->
          <div v-else-if="comparisonResult && totalOperations === 0" class="no-changes">
            <el-empty description="没有检测到数据变更">
              <template #image>
                <el-icon size="60" color="#909399"><Check /></el-icon>
              </template>
              <template #description>
                <p>Excel文件中的数据与现有数据完全一致，无需执行任何操作</p>
              </template>
              <el-button type="primary" @click="clearFile">重新选择文件</el-button>
            </el-empty>
          </div>
        </div>
      </div>

      <!-- 确认提交步骤 -->
      <div v-else-if="currentStep === 2" class="confirm-section">
        <!-- 提交确认组件 -->
        <SubmissionConfirm
          :valid-count="allValidRows.length"
          :invalid-count="allInvalidRows.length"
          :total-count="allValidRows.length + allInvalidRows.length"
          :rule-name="ruleName"
          :rule-key="ruleKey"
          :upload-mode="uploadMode"
          :operation-summary="operationSummary"
          :submitting="submitting"
          @prev-step="prevStep"
          @confirm="confirmSubmission"
        />
      </div>

      <!-- 任务处理步骤 -->
      <div v-else-if="currentStep === 3" class="task-section">
        <!-- 上传进度组件 -->
        <UploadProgress
          :status="uploadStatus"
          :progress="taskProgress"
          :progress-message="taskProgressMessage"
          :progress-detail="taskProgressDetail"
          :task-info="taskInfo"
          :result-stats="taskResultStats"
          :refreshing="taskRefreshing"
          @refresh="refreshTaskStatus"
          @cancel="cancelTask"
          @complete="goBack"
        />
      </div>

      <!-- 完成步骤 -->
      <div v-else-if="currentStep === 4" class="completion-section">
        <!-- 如果有提交结果，显示详细结果 -->
        <SubmissionResult
          v-if="submissionResult"
          :result="submissionResult"
          :rule-key="ruleKey"
          @view-details="handleViewDetails"
          @new-upload="handleRestart"
        />
        <!-- 否则显示任务进度（兼容旧版本） -->
        <UploadProgress
          v-else
          :task-info="taskInfo"
          :result-stats="taskResultStats"
          :step-status="stepStatus"
          @restart="handleRestart"
        />
      </div>
    </el-card>

    <!-- Edit Dialog -->
    <el-dialog v-model="isDialogVisible" :title="`编辑: ${editingFieldLabel}`" width="50%">
        <el-input
            v-model="editingValue"
            type="textarea"
            :rows="10"
            placeholder="请输入内容"
        />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="isDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveEdit">保存并重新校验</el-button>
            </span>
        </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { get } from '../api/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as XLSX from 'xlsx'
import CryptoJS from 'crypto-js'
import {
  ArrowLeft
} from '@element-plus/icons-vue'

// 组件导入
import RegistrationSteps from '../components/business/RegistrationSteps.vue'
import IncrementalDataComparison from '../components/IncrementalDataComparison.vue'

// 新的重构组件导入 - 使用别名路径确保正确解析
import UploadModeSelector from '@/components/upload/UploadModeSelector.vue'
import FileUploadArea from '@/components/upload/FileUploadArea.vue'
import SubmissionConfirm from '@/components/upload/SubmissionConfirm.vue'
import UploadProgress from '@/components/upload/UploadProgress.vue'
import SubmissionResult from '@/components/upload/SubmissionResult.vue'

// Composables导入
import { useRegistrationTask } from '../composables/business/useRegistrationTask'
import { useIncrementalUpload } from '../composables/business/useIncrementalUpload'
import { confirmRuleSubmission } from '../api/rules'
import { useAppStore } from '../stores/app'
import { storeToRefs } from 'pinia'

// 类型定义和字段映射
import { getFieldChineseName } from '../types/generated-fields'
import { fieldMappingEngine } from '../utils/fieldMappingEngine'
import { convertExcelRowArrayFields, isArrayField, validateArrayField } from '../utils/arrayFieldConverter'

const props = defineProps({
  ruleKey: String
})

const router = useRouter()

// 全局状态管理
const appStore = useAppStore()
const { globalLoading, hasError, currentError } = storeToRefs(appStore)

// 统一错误处理
const handleError = (error, message = '操作失败') => {
  console.error(message, error)
  ElMessage.error(`${message}: ${error.message || '请稍后重试'}`)

  // 设置全局错误状态
  appStore.setError({
    type: 'UPLOAD_ERROR',
    message: message,
    details: { error: error.message || error, ruleKey: props.ruleKey }
  })
}

// 基础数据
const activeTab = ref('valid')
const schema = ref([])
const excelHeaders = ref([])
const ruleName = ref('')

// 上传模式
const uploadMode = ref('full') // 'full' | 'incremental'

// 步骤管理
const currentStep = ref(0) // 0: 选择文件, 1: 数据预览, 2: 确认提交, 3: 任务处理, 4: 完成
const stepStatus = ref('process')

// 文件状态管理
const fileStatus = ref('ready') // ready, processing, success, error

// 上传状态管理
const uploadStatus = computed(() => {
  if (currentStep.value === 3) {
    if (submitting.value || processing.value) return 'uploading'
    if (stepStatus.value === 'finish') return 'success'
    if (stepStatus.value === 'error') return 'error'
  }
  return 'pending'
})

// 文件和数据
const selectedFile = ref(null)
const processing = ref(false)
const allValidRows = ref([])
const allInvalidRows = ref([])

// 分页
const pageSize = 15
const validCurrentPage = ref(1)
const invalidCurrentPage = ref(1)

// 编辑对话框
const isDialogVisible = ref(false)
const editingItem = ref(null)
const editingField = ref('')
const editingFieldLabel = ref('')
const editingValue = ref('')

// 提交相关
const submitting = ref(false)
const registrationTaskId = ref(null)
const submissionResult = ref(null)

// 增量上传相关
const {
  comparisonResult,
  operationSummary,
  totalOperations,
  isComparing,
  isUploading: isIncrementalUploading,
  compareData,
  executeIncrementalUpload,
  resetState: resetIncrementalState
} = useIncrementalUpload(props.ruleKey)

// 文件验证函数已移到 FileUploadArea 组件中

const validateRuleKey = () => {
  if (!props.ruleKey || props.ruleKey.trim() === '') {
    ElMessage.error('规则键不能为空')
    return false
  }
  return true
}

// 新增的组件事件处理方法
const handleModeChange = (mode) => {
  uploadMode.value = mode
  // 如果切换模式，清理现有状态
  if (selectedFile.value) {
    clearFile()
  }
}

const handleFileChange = (file) => {
  selectedFile.value = file
  fileStatus.value = 'ready'
  // 清理之前的数据
  allValidRows.value = []
  allInvalidRows.value = []

  // 重置增量上传状态
  if (uploadMode.value === 'incremental') {
    resetIncrementalState()
  }
}

// 增强的行数据验证函数，集成字段映射引擎
const validateRowData = (rowData, rowNumber) => {
  try {
    // 使用字段映射引擎进行数据转换和验证
    const transformedData = fieldMappingEngine.transformToApi(rowData)

    // 验证必填字段
    const requiredFields = schema.value.filter(field => field.required)
    for (const field of requiredFields) {
      const value = transformedData[field.name_en]
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        return {
          isValid: false,
          error: `${getFieldChineseName(field.name_en)}不能为空`,
          data: rowData
        }
      }
    }

    // 验证字段值格式
    for (const field of schema.value) {
      const value = transformedData[field.name_en]
      if (value !== undefined && value !== null && value !== '') {
        // 基本字段验证（简化版）
        if (field.required && (value === null || value === undefined || value === '')) {
          return {
            isValid: false,
            error: `${getFieldChineseName(field.name_en)}: 必填字段不能为空`,
            data: rowData
          }
        }
      }
    }

    return {
      isValid: true,
      error: '',
      data: transformedData
    }
  } catch (error) {
    return {
      isValid: false,
      error: `行 ${rowNumber}: ${error.message}`,
      data: rowData
    }
  }
}

// revalidateRow 函数在下方已定义，删除重复声明

// 任务管理
const {
  taskInfo,
  refreshing: taskRefreshing,
  fetchTaskStatus,
  cancelTask: cancelRegistrationTask,
  pollTaskUntilComplete
} = useRegistrationTask()

// API_KEY现在通过request.js统一处理，不需要在这里定义

const paginatedValidRows = computed(() => {
    const start = (validCurrentPage.value - 1) * pageSize;
    return allValidRows.value.slice(start, start + pageSize);
})

const paginatedInvalidRows = computed(() => {
    const start = (invalidCurrentPage.value - 1) * pageSize;
    return allInvalidRows.value.slice(start, start + pageSize);
})

const tableColumns = computed(() => {
    return schema.value
        .filter(s => s.name_en !== 'rule_id') // rule_id is generated, not displayed for editing
        .map(s => ({
            prop: s.name_en,
            label: getFieldChineseName(s.name_en) || s.name_cn,
            type: s.type,
            required: s.required
        }))
})

// 任务相关计算属性
const taskProgress = computed(() => {
  return taskInfo.value?.progress_percentage || 0
})

const taskProgressStatus = computed(() => {
  if (!taskInfo.value) return ''
  const status = taskInfo.value.status
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return ''
})

// taskProgressMessage 和 taskProgressDetail 在下方重新定义，这里删除重复声明

const taskResultStats = computed(() => {
  if (!taskInfo.value?.result_data) return null
  return {
    success: taskInfo.value.result_data.success_count || 0,
    failed: taskInfo.value.result_data.failed_count || 0,
    total: taskInfo.value.result_data.total_count || 0
  }
})

// operationSummary 已经从 useIncrementalUpload() 中解构获得，无需重复定义

const taskProgressMessage = computed(() => {
  return taskInfo.value?.current_message || '正在处理注册任务...'
})

const taskProgressDetail = computed(() => {
  if (!taskInfo.value) return ''
  const completed = taskInfo.value.completed_operations || 0
  const total = taskInfo.value.total_operations || 0
  return `${completed} / ${total} 操作已完成`
})

const isTaskSuccess = computed(() => {
  return taskInfo.value?.status === 'completed'
})

const taskResultMessage = computed(() => {
  if (!taskInfo.value) return ''
  if (taskInfo.value.status === 'completed') {
    return '规则注册任务已成功完成'
  } else if (taskInfo.value.status === 'failed') {
    return taskInfo.value.error_message || '规则注册任务执行失败'
  }
  return ''
})

const truncate = (text, length = 50) => {
    if (text === null || text === undefined) return '';
    // Handle arrays specifically for display purposes
    const strText = Array.isArray(text) ? text.join(', ') : String(text);
    if (strText.length <= length) return strText;
    return strText.substring(0, length) + '...';
}

const openEditDialog = (item, field) => {
    editingItem.value = item;
    editingField.value = field;
    editingFieldLabel.value = tableColumns.value.find(c => c.prop === field)?.label || field;
    editingValue.value = item.data[field];
    isDialogVisible.value = true;
};

const saveEdit = () => {
    if (editingItem.value && editingField.value) {
        editingItem.value.data[editingField.value] = editingValue.value;
        revalidateRow(editingItem.value);
    }
    isDialogVisible.value = false;
};

const fetchRuleInfo = async () => {
    try {
        // 从规则状态列表中获取规则名称
        const response = await get('/v1/rules/status');

        // 处理统一API响应格式
        let statusData = []
        if (response && typeof response === 'object') {
            if (response.success && response.data) {
                // 新的统一格式：{success: true, data: [...]}
                statusData = response.data
            } else if (Array.isArray(response)) {
                // 旧格式：直接是数组
                statusData = response
            }
        }

        if (statusData && Array.isArray(statusData)) {
            const rule = statusData.find(r => r.rule_key === props.ruleKey);
            if (rule && rule.rule_name) {
                ruleName.value = rule.rule_name;
            }
        }
    } catch (error) {
        handleError(error, '获取规则信息失败');
        // 如果获取失败，保持使用ruleKey
    }
}

const fetchSchema = async () => {
    try {
        const schemaData = await get(`/v1/rules/${props.ruleKey}/schema`);
        schema.value = schemaData;
    } catch (error) {
        console.error('获取规则校验schema失败:', error);
        ElMessage.error('无法加载规则校验定义，请返回重试。');
    }
}

const initializeComponent = async () => {
    await Promise.all([
        fetchRuleInfo(),
        fetchSchema()
    ]);
}

// 监听任务状态变化
watch(() => taskInfo.value?.status, (newStatus, oldStatus) => {
  if (newStatus && newStatus !== oldStatus) {
    console.log('任务状态变化:', oldStatus, '->', newStatus)

    if (newStatus === 'completed') {
      ElMessage.success('规则注册任务已完成')
      currentStep.value = 4
      stepStatus.value = 'finish'
    } else if (newStatus === 'failed') {
      ElMessage.error('规则注册任务执行失败')
      currentStep.value = 4
      stepStatus.value = 'error'
    }
  }
})

onMounted(initializeComponent);

// 旧的文件处理方法已移到 FileUploadArea 组件中

const clearFile = () => {
  selectedFile.value = null
  allValidRows.value = []
  allInvalidRows.value = []
  currentStep.value = 0
  fileStatus.value = 'ready'

  // 重置增量上传状态
  if (uploadMode.value === 'incremental') {
    resetIncrementalState()
  }
}

const processFile = (file) => {
  // 验证规则键
  if (!validateRuleKey()) {
    return
  }

  const fileToProcess = file || selectedFile.value
  if (!fileToProcess) {
    ElMessage.error('请先选择文件')
    return
  }

  try {
    processing.value = true
    fileStatus.value = 'processing'
    appStore.addLoadingTask('parseFile', '正在解析文件...')
    parseExcelFile(fileToProcess)
  } catch (error) {
    handleError(error, '文件处理失败')
    processing.value = false
    fileStatus.value = 'error'
  }
}

const parseExcelFile = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
        try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            processData(jsonData);
        } catch (error) {
            console.error("文件解析失败:", error);
            ElMessage.error("文件解析失败，请确保文件格式正确。")
            processing.value = false
            fileStatus.value = 'error'
            appStore.removeLoadingTask('parseFile')
        }
    };
    reader.readAsArrayBuffer(file);
}

// 步骤导航方法
const nextStep = () => {
  if (currentStep.value < 4) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}



const validateAndTransformRow = (rowData) => {
    const transformedData = {};

    for (const s of schema.value) {
        const engKey = s.name_en;
        const chnKey = s.name_cn;
        const value = rowData[engKey];

        // 1. Validation Logic
        if (s.required && (value === null || value === undefined || String(value).trim() === '')) {
            return { valid: false, error: `'${chnKey}' 是必填项。`, data: rowData };
        }

        // Skip further checks for non-required empty values
        if (!s.required && (value === null || value === undefined || String(value).trim() === '')) {
            transformedData[engKey] = value;
            continue;
        }

        const valueStr = String(value);

        switch (s.type) {
            case 'int':
                if (!/^-?\d+$/.test(valueStr)) {
                    return { valid: false, error: `'${chnKey}' 的值 "${value}" 必须是整数。`, data: rowData };
                }
                break;
            case 'list[str]':
                // No specific validation needed, as any string can be split.
                break;
            // Future type validations can be added here
        }

        // 2. Transformation Logic
        switch (s.type) {
            case 'int':
                transformedData[engKey] = parseInt(valueStr, 10);
                break;
            case 'list[str]':
                // 统一使用数组字段转换工具
                if (isArrayField(engKey)) {
                    const validationResult = validateArrayField(engKey, value)
                    if (!validationResult.valid) {
                        return { valid: false, error: `'${chnKey}' ${validationResult.error}`, data: rowData };
                    }
                    transformedData[engKey] = validationResult.converted || []
                } else {
                    // 非数组字段的列表处理（备用逻辑）
                    transformedData[engKey] = valueStr.replace(/[\[\]]/g, '').split(/[,;，；]/).map(item => item.trim().replace(/["']+/g, '')).filter(Boolean);
                }
                break;
            default:
                transformedData[engKey] = value;
                break;
        }
    }

    // Generate ID post-transformation
    const ruleNameValue = transformedData.rule_name;
    if (!ruleNameValue) {
        return { valid: false, error: "'规则名称' 字段缺失，无法生成ID。", data: rowData };
    }
    transformedData.rule_id = CryptoJS.MD5(String(ruleNameValue)).toString();

    return { valid: true, data: transformedData, error: null };
};

const processData = (jsonData) => {
    allValidRows.value = [];
    allInvalidRows.value = [];

    // 检查数据是否存在
    if (!jsonData || !Array.isArray(jsonData)) {
        ElMessage.error('文件解析失败，请检查文件格式。');
        processing.value = false;
        fileStatus.value = 'error';
        appStore.removeLoadingTask('parseFile');
        return;
    }

    if (jsonData.length < 2) {
        ElMessage.warning('Excel文件为空或只有表头。');
        processing.value = false;
        fileStatus.value = 'error';
        appStore.removeLoadingTask('parseFile');
        return;
    }

    const headers = jsonData[0];
    excelHeaders.value = headers;
    const dataRows = jsonData.slice(1);

    const chnToEngMap = schema.value.reduce((acc, s) => {
        acc[s.name_cn] = s.name_en;
        return acc;
    }, {});

    dataRows.forEach((row, index) => {
        const rowDataChn = headers.reduce((obj, header, i) => {
            obj[header] = row[i];
            return obj;
        }, {});

        const rowDataEng = {};
        for (const chnKey in rowDataChn) {
            const engKey = chnToEngMap[chnKey];
            if (engKey) {
                rowDataEng[engKey] = rowDataChn[chnKey];
            }
        }

        const result = validateAndTransformRow(rowDataEng);
        if (result.valid) {
            allValidRows.value.push(result.data);
        } else {
            allInvalidRows.value.push({
                rowNumber: index + 2,
                error: result.error,
                data: rowDataEng, // Use original data for invalid rows to allow editing
                id: Math.random()
            });
        }
    });

    if (allInvalidRows.value.length > 0) {
        activeTab.value = 'invalid';
    } else {
        activeTab.value = 'valid';
    }

    processing.value = false
    fileStatus.value = 'success'
    appStore.removeLoadingTask('parseFile')
    ElMessage.success(`解析完成! ${allValidRows.value.length} 条有效, ${allInvalidRows.value.length} 条无效。`);

    // 自动进入数据预览步骤
    currentStep.value = 1

    // 如果是增量上传模式且有有效数据，自动执行数据对比
    if (uploadMode.value === 'incremental' && allValidRows.value.length > 0 && allInvalidRows.value.length === 0) {
        handleIncrementalDataComparison()
    }
};

const revalidateRow = (invalidRow) => {
    const result = validateAndTransformRow(invalidRow.data);
    if (result.valid) {
        allValidRows.value.push(result.data);
        allInvalidRows.value = allInvalidRows.value.filter(r => r.id !== invalidRow.id);
        ElMessage.success(`行 ${invalidRow.rowNumber} 已修复并通过校验!`);
        if (allInvalidRows.value.length === 0) {
            activeTab.value = 'valid';
        }
    } else {
        invalidRow.error = result.error;
    }
};

const confirmSubmission = async () => {
    if (allInvalidRows.value.length > 0) {
        ElMessage.error('仍有无效数据，无法提交。');
        return;
    }

    try {
        submitting.value = true

        // 修复：确保所有数组字段都被正确转换
        const processedData = allValidRows.value.map(row => {
            console.log('数组字段转换前:', row)
            const convertedData = convertExcelRowArrayFields(row)
            console.log('数组字段转换后:', convertedData)
            return convertedData
        })

        console.log('最终提交的数据结构:', processedData)

        const response = await confirmRuleSubmission(props.ruleKey, {
            user_id: "frontend_user", // This should be from a real auth system
            data_to_submit: processedData
        })

        // 处理新的响应格式，显示详细的操作统计
        if (response.operation_details) {
            const details = response.operation_details
            const messages = []

            if (details.created_count > 0) {
                messages.push(`新建 ${details.created_count} 条记录`)
            }
            if (details.updated_count > 0) {
                messages.push(`更新 ${details.updated_count} 条记录`)
            }
            if (details.skipped_count > 0) {
                messages.push(`跳过 ${details.skipped_count} 条重复记录`)
            }
            if (details.failed_count > 0) {
                messages.push(`失败 ${details.failed_count} 条记录`)
            }

            const summaryMessage = messages.length > 0
                ? `数据提交完成！${messages.join('，')}`
                : '数据提交成功！'

            ElMessage.success(summaryMessage)
        } else {
            ElMessage.success('数据提交成功！')
        }

        // 保存响应数据用于结果展示
        submissionResult.value = response

        // 检查是否有注册任务ID
        if (response.registration_task_id) {
            registrationTaskId.value = response.registration_task_id
            currentStep.value = 3 // 进入任务处理步骤

            // 开始轮询任务状态
            pollTaskUntilComplete(response.registration_task_id, {
                onProgress: (taskInfo) => {
                    // 任务进度更新时的回调
                    console.log('任务进度更新:', taskInfo)
                }
            }).then((finalTaskInfo) => {
                // 任务完成后进入完成步骤
                currentStep.value = 4
                if (finalTaskInfo?.status === 'completed') {
                    stepStatus.value = 'finish'
                } else {
                    stepStatus.value = 'error'
                }
            })
        } else {
            // 没有注册任务，直接完成
            currentStep.value = 4
            stepStatus.value = 'finish'
        }

    } catch (error) {
        console.error('提交失败:', error);
        ElMessage.error('提交失败，请稍后重试。');
        stepStatus.value = 'error'
    } finally {
        submitting.value = false
    }
}

// 任务状态刷新
const refreshTaskStatus = async () => {
  if (registrationTaskId.value) {
    await fetchTaskStatus(registrationTaskId.value)
  }
}

// 取消任务
const cancelTask = async () => {
  if (!registrationTaskId.value) return

  try {
    const confirmed = await ElMessageBox.confirm(
      '确定要取消当前注册任务吗？',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    if (confirmed) {
      await cancelRegistrationTask(registrationTaskId.value)
      ElMessage.success('任务已取消')
      currentStep.value = 4
      stepStatus.value = 'error'
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
    }
  }
}

const goBack = () => {
    router.push({ name: 'Dashboard' });
};

// 增量上传相关函数
const handleIncrementalDataComparison = async () => {
  try {
    if (allValidRows.value.length === 0) {
      ElMessage.warning('没有有效数据进行对比')
      return
    }

    await compareData(allValidRows.value)
  } catch (error) {
    handleError(error, '数据对比失败')
  }
}

const handleIncrementalConfirm = async (userConfirmations) => {
  try {
    const result = await executeIncrementalUpload(userConfirmations)

    if (result.success) {
      // 增量上传成功，进入完成步骤
      currentStep.value = 4
      stepStatus.value = 'finish'
      ElMessage.success('增量上传完成！')
    }
  } catch (error) {
    handleError(error, '增量上传失败')
    stepStatus.value = 'error'
  }
}

const handleIncrementalCancel = () => {
  // 返回到文件选择步骤
  currentStep.value = 0
  clearFile()
}

// 结果页面处理函数
const handleViewDetails = (ruleKey) => {
  // 跳转到规则详情页面
  router.push(`/rules/${ruleKey}/details`)
}

const handleRestart = () => {
  // 重置所有状态，开始新的上传
  currentStep.value = 0
  stepStatus.value = 'process'
  submissionResult.value = null
  registrationTaskId.value = null
  fileStatus.value = 'ready'
  clearFile()
}
</script>

<style scoped>
.data-uploader {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
  border: none;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-subtitle {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.content-card {
  margin-top: 20px;
  border-radius: 8px;
}

.upload-section, .preview-section, .confirm-section, .task-section {
  padding: 20px;
}

/* 确认区域样式 */
.confirmation-section {
  margin-top: 24px;
  padding: 20px;
  text-align: center;
  border-top: 1px solid var(--el-border-color-lighter);
}

.task-section {
  min-height: 300px;
}

/* 增量上传预览样式 */
.incremental-upload-preview {
  padding: 20px;
}

/* 保留必要的全局样式 */

/* 增量对比预览样式 */
.comparing-status {
  padding: 40px 20px;
}

.loading-card {
  text-align: center;
  border: none;
  box-shadow: none;
  background-color: var(--el-bg-color-page);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text h3 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.loading-text p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.no-changes {
  padding: 40px 20px;
  text-align: center;
}

/* 数据预览样式 */
.data-preview-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-stats {
  display: flex;
  gap: 8px;
}

.data-grid {
  display: grid;
  gap: 16px;
  padding: 16px 0;
  grid-template-columns: 1fr;
}

.data-item-card {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 16px;
  background-color: var(--el-bg-color-page);
  transition: all 0.3s ease;
}

.data-item-card:hover {
  box-shadow: var(--el-box-shadow-light);
  transform: translateY(-2px);
}

.data-item-card.invalid {
  border-color: var(--el-color-error-light-7);
  background-color: var(--el-color-error-light-9);
}

.error-alert {
  margin-bottom: 12px;
}

/* el-descriptions 样式调整 - 三列布局，每列包含字段名+字段值 */
:deep(.el-descriptions__table) {
  table-layout: fixed;
  width: 100%;
}

:deep(.el-descriptions__row) {
  min-height: 40px !important;
  height: auto !important;
}

:deep(.el-descriptions__cell) {
  vertical-align: top !important;
  height: auto !important;
  min-height: 40px !important;
}

:deep(.el-descriptions__label) {
  width: 20% !important;
  text-align: right !important;
  padding: 8px 8px 8px 12px !important;
  background-color: var(--el-fill-color-lighter) !important;
  font-weight: 500;
  border-right: 1px solid var(--el-border-color-light);
  word-break: break-all;
  line-height: 1.2;
  white-space: normal;
  overflow-wrap: break-word;
  /* 移除max-width限制，让内容在20%宽度内自然换行 */
}

:deep(.el-descriptions__content) {
  width: 30% !important;
  padding: 8px 12px 8px 8px !important;
  word-break: break-word;
  border-right: 1px solid var(--el-border-color-light);
  line-height: 1.2;
  /* 移除CSS层面的截断，让JavaScript完全控制 */
}

.field-value {
  display: block;
  line-height: 1.4;
  word-break: break-word;
  min-height: 20px;
  /* 移除固定高度，允许根据内容自动调整 */
}

/* 字段值截断长度控制 - 可在此处调整截断字符数 */
.field-value-container {
  width: 100%;
  height: 24px;
  overflow: hidden;
}

.field-value.truncated {
  cursor: pointer;
  color: var(--el-text-color-primary);
}

.editable-cell {
  position: relative;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.editable-cell:hover {
  background-color: var(--el-fill-color-light);
}

.edit-icon {
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
  color: var(--el-color-primary);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.editable-cell:hover .edit-icon {
  opacity: 1;
}

.item-actions {
  margin-top: 12px;
  text-align: center;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>

<style>
/* 全局样式：多行tooltip */
.multiline-tooltip {
    max-width: 400px !important;
    white-space: pre-wrap !important;
    word-break: break-word !important;
    line-height: 1.5 !important;
}

.multiline-tooltip .el-tooltip__popper-content {
    white-space: pre-wrap !important;
    word-break: break-word !important;
    max-height: 200px;
    overflow-y: auto;
}
</style>